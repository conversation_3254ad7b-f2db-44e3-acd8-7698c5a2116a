<template>
  <!-- 任务统计列表 -->
  <UmvContent>
    <!-- 查询 -->
    <UmvQuery
      v-model="queryForm"
      :opts="queryOpts"
      label-width="90px"
      @check="handleSearch"
      @reset="handleReset"
    />

    <!-- 列表 -->
    <UmvTable v-loading="loading" :data="tableData" :columns="columns" @refresh="queryOrderList">
      <!-- 自定义工具栏 -->
      <template #tools>
        <el-button type="primary" size="small" v-track:click.btn @click="exportFile">
          导出
        </el-button>
      </template>

      <!-- 分页器 -->
      <template #pagination>
        <Pagination
          :current-page="query.page"
          :page-size="query.pageSize"
          :total="query.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </template>
    </UmvTable>
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'ImageReviewBilling'
})

const { t } = useI18n()
import { useRouter } from 'vue-router'
import { getPageAuditListApi, getPageAuditListExportApi } from '@/api/PictureAuditBillService'

import UmvContent from '@/components/UmvContent'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { ElDatePicker, ElButton, ElSelect, ElOption } from 'element-plus'
import { CustomerSelect } from '@/components/CustomerSelect/index'
import { useCustomerReviewSearch } from '@/views/RuleManage/common/useCustomerReviewSearch'

const loading = ref(false)
const router = useRouter()

// 使用客户和图审服务选择功能
const { customerId, reviewNo, reviewConfiglist, changeCusNo, changeReviewNo } =
  useCustomerReviewSearch()

// 查询表单数据
const queryForm = ref({
  customerId: '',
  reviewNo: '',
  createTime: [] // 创建时间
})

// 监听客户和图审服务的变化，同步到查询表单
watch(
  [customerId, reviewNo],
  ([newCustomerId, newReviewNo]) => {
    queryForm.value.customerId = newCustomerId
    queryForm.value.reviewNo = newReviewNo
  },
  { immediate: true }
)

// 日期面板选中首次日期记录的值
const chooseDay = ref<number | null>(null)
const datePickerChange = (val: any) => {
  const [pointDay] = val
  chooseDay.value = new Date(pointDay).getTime()
}

/** 获取焦点时清除首次选中的日期值 **/
const datePickerFocus = async () => {
  chooseDay.value = null
}
const datePickerDisabled = (date: Date) => {
  if (chooseDay.value) {
    const chooseDate = new Date(chooseDay.value)
    const chooseYear = chooseDate.getFullYear()
    const chooseDateStart = new Date(new Date(chooseDate).setFullYear(chooseYear - 1)).getTime()
    const chooseDateEnd = new Date(new Date(chooseDate).setFullYear(chooseYear + 1)).getTime()
    const currDate = new Date(date).getTime()
    // 禁用条件：当前日期超出选中日期一年前后以及超出当前日期
    return currDate < chooseDateStart || currDate > chooseDateEnd || currDate > new Date().getTime()
  }
  return new Date(date).getTime() > new Date().getTime()
}

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  customerNo: {
    label: '客户',
    defaultVal: '',
    controlRender: () => (
      <CustomerSelect
        v-model={customerId.value}
        clearable
        filterable
        placeholder="请选择客户"
        style="width: 100%"
        onChange={changeCusNo}
      />
    )
  },
  reviewNo: {
    label: '图审服务',
    defaultVal: '',
    controlRender: () => (
      <ElSelect
        v-model={reviewNo.value}
        clearable
        filterable
        placeholder="请选择图审服务"
        style="width: 100%"
        onChange={changeReviewNo}
      >
        {reviewConfiglist.value.map((item) => (
          <ElOption key={item.reviewNo} label={item.reviewName} value={item.reviewNo} />
        ))}
      </ElSelect>
    )
  },
  createTime: {
    label: '提交时间',
    defaultVal: [],
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.createTime}
        type="daterange"
        disabled-date={datePickerDisabled}
        onCalendar-change={datePickerChange}
        onFocus={datePickerFocus}
        value-format="YYYY-MM-DD"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 100%"
      />
    )
  }
})

const query = reactive({
  total: 0,
  page: 1,
  pageSize: 10
})
const tableData = ref([{}]) // 数据列表
// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '60px',
    renderTemplate: (scope) => <span>{scope.$index + 1}</span>
  },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '100px'
  },
  {
    prop: 'reviewName',
    label: '图审服务',
    minWidth: '100px'
  },
  {
    prop: 'totalNum',
    label: '任务数量',
    minWidth: '100px'
  },
  {
    prop: 'successNum',
    label: '成功数量',
    width: '100px'
  },
  {
    prop: 'falseNum',
    label: '失败数量',
    minWidth: '90px'
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '80px',
    renderTemplate: (scope) => (
      <ElButton link type="primary" onClick={() => openDialog('viewOrder', scope.row)}>
        明细
      </ElButton>
    )
  }
])

onMounted(() => {
  queryOrderList()
})

// 获取订单列表
const queryOrderList = async () => {
  const data = {
    customerNo: queryForm.value.customerId, // 用户
    reviewNo: queryForm.value.reviewNo, // 商户
    startTime: queryForm.value?.createTime?.[0] ?? '', // 订单创建时间
    endTime: queryForm.value?.createTime?.[1] ?? '', // 订单创建时间
    pageNum: query.page,
    pageSize: query.pageSize
  }
  loading.value = true
  try {
    const resData = await getPageAuditListApi(data)
    tableData.value = resData?.list || []
    query.total = resData?.total || 0
  } catch (error) {
  } finally {
    loading.value = false
  }
}

// 打开弹窗
const openDialog = (type = '', obj: any = {}) => {
  switch (type) {
    // 查看详情
    case 'viewOrder':
      router.push({
        path: '/RuleManage/ImageReviewBillingDetail',
        query: {
          customerId: obj.customerNo,
          reviewNo: obj.reviewNo,
          startTime: queryForm.value?.createTime?.[0] ?? '', // 订单创建时间
          endTime: queryForm.value?.createTime?.[1] ?? '' // 订单创建时间
        }
      })
      break
  }
}

const exportFile = async () => {
  const data = {
    customerNo: queryForm.value.customerId, // 用户
    reviewNo: queryForm.value.reviewNo, // 商户
    startTime: queryForm.value?.createTime?.[0] ?? '', // 订单创建时间
    endTime: queryForm.value?.createTime?.[1] ?? '' // 订单创建时间
  }
  loading.value = true
  try {
    const res = await getPageAuditListExportApi(data)
    const blob = res.data // 文件流
    const disposition = res.headers['content-disposition'].split('filename=')[1] // 请求响应头提取文件名
    const fileName = decodeURIComponent(disposition) // 转码
    window.URL = window.URL || window.webkitURL
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.download = fileName
    link.click()
    window.URL.revokeObjectURL(url)
    // console.log(data)
    // const downUrl = res.request.responseURL // 下载链接
    // window.location.href = downUrl
  } catch (error) {
  } finally {
    loading.value = false
  }
}
// 查询
const handleSearch = () => {
  // if (!searchForm.sceneId && !searchForm.merchantId) {
  //   $message.error('请先选择查询条件在进行查询')
  //   return
  // }
  // typeName = searchForm.sceneId ? '场景名称' : '商户名称'
  query.page = 1
  queryOrderList()
}
// 重置
const handleReset = async () => {
  // 重置客户选择，这会触发hook中的watch，自动处理图审服务
  customerId.value = ''
  reviewNo.value = ''
  // 重置其他表单字段
  queryForm.value.customerId = ''
  queryForm.value.reviewNo = ''
  queryForm.value.createTime = []
  // queryOrderList()
}
// 分页导航
const handlePageChange = (val: number) => {
  query.page = val
  queryOrderList()
}
const handleSizeChange = (val: number) => {
  query.page = 1
  query.pageSize = val
  queryOrderList()
}
</script>
<style scoped lang="less">
.m-task-statistical-list-wrap {
  :deep(.el-select),
  :deep(.el-cascader) {
    width: 100%;
  }
  :deep(.el-date-editor) {
    width: 100%;
  }
  :deep(.el-select .el-input) {
    min-width: 130px;
  }
  :deep(.input-with-select .el-input-group__prepend) {
    background-color: #fff;
  }
}
</style>
