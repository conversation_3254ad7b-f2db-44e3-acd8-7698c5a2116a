<template>
  <div
    v-if="isAi"
    class="image-box"
    :class="{
      heng: cardStyle == 0,
      shu: cardStyle == 1
    }"
  >
    <div
      v-for="(item, index) in list"
      :key="index"
      class="image-li"
      :class="{ 'grid-row-1': index == 0 && cardStyle == 0 }"
    >
      <el-image
        class="flex-1"
        fit="scale-down"
        :preview-teleported="true"
        :src="item.image"
        :preview-src-list="list.map((item) => item.image)"
        :initial-index="index"
        alt=""
      />
      <div class="title-text">{{ item.title }}</div>
    </div>
  </div>
  <div v-else class="image-box-noAi">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="image-li"
      :style="{ height: cardStyle == 0 ? '100%' : '85%' }"
    >
      <el-image
        fit="scale-down"
        :preview-teleported="true"
        :src="item.image"
        :preview-src-list="list.map((item) => item.image)"
        :initial-index="index"
        alt=""
      />
      <div class="title-text">{{ item.title }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  list: { image: string; title: string }[]
}>()
//横是0，竖是1
const cardStyle = ref(0)
watchEffect(() => {
  if (props.list?.length > 0) {
    const image = props.list?.[0].image
    if (image) {
      const img = new Image()
      img.src = image
      img.onload = function () {
        cardStyle.value = img.width > img.height ? 0 : 1
      }
    }
  }
})
const isAi = computed(() => props?.list?.length === 3)
console.log('props', props.list)
</script>
<style scoped lang="scss">
.image-box {
  max-height: 80vh;
}
.image-box {
  display: grid;
}
.heng {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 50%);
}
.shu {
  grid-template-columns: repeat(3, 1fr);
}
.image-li {
  padding-right: 16px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
}
.grid-row-1 {
  grid-row: 1/3;
}
.title-text {
  padding: 10px 0;
  font-size: 22px;
  font-weight: bold;
}
.image-box-noAi {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
</style>
