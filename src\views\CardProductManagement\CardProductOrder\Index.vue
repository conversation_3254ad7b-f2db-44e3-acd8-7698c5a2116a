<script setup lang="tsx">
import router from '@/router'
import type { FormRules } from 'element-plus'
import { searchOrders, fastSearchApi, cancelApi } from '@/api/Order'
import { getDictLabel, getDictOptions } from '@/utils/dict'
import { cloneDeep } from 'lodash-es'
import { orderSourceList } from './common/lib'
import { formatMoneyDigitsEx } from '@/utils/formatMoney'
import { SearchWildcardInput } from '@/components/SearchWildcardInput'
import UmvContent from '@/components/UmvContent'
import { UmvQuery, type QueryOption, type QueryForm } from '@/components/UmvQuery'
import UmvTable from '@/components/UmvTable'
import type { TableColumn } from '@/components/UmvTable/src/types'

import { useComonHook } from '@/views/DataBoardScreen/common/useComonHook'

defineOptions({
  name: 'CardProductOrder'
})

const { industryListBatchCard } = useComonHook()

const { t } = useI18n()
const tableLoading = ref<boolean>(false)
interface IqueryData {
  customerName: string
  customerId: string
  orderSource: string
  orderType: string
  orderStatus: string
  orderCode: string
  vname: string
  createUserName: string
  taskCode: string
  selfOnly: boolean | undefined

  createOrderTime: Date[]
  customerIndustry: string
}

const queryData = ref<IqueryData>({
  customerName: '',
  customerId: '',
  orderSource: '',
  orderType: '',
  orderStatus: '',
  orderCode: '',
  vname: '',
  createUserName: '',
  taskCode: '',
  selfOnly: false,

  createOrderTime: [],
  customerIndustry: ''
})
const rules = reactive<FormRules>({
  customerId: [{ required: false, message: '请选择客户', trigger: 'change' }]
})

// 订单类型
const orderTypeOptions = ref(getDictOptions('order_type'))

// 订单来源
const orderSourceEnum = ['管理端', '客户端', '销售端']

// 订单来源-对象
const orderSource = {
  management: orderSourceEnum[0],
  customer: orderSourceEnum[1],
  sale: orderSourceEnum[2]
}

const tableData = ref([])
const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})
const total = ref(0)
const getList = async () => {
  try {
    tableLoading.value = true
    //const res = await batchAndSamplePageApi(queryData, pagination)
    const res = await searchOrders(queryData.value, pagination)
    tableData.value = res?.list?.map((item) => {
      const productIdMaps = {}
      item.productTypeCount = 0
      item.orderDetailExt?.productionList?.forEach((product) => {
        ++item.productTypeCount
        if (undefined === productIdMaps[product.productId]) {
          productIdMaps[product.productId] = product.productId
        }
      })

      return item
    })
    total.value = res?.total || 0
  } catch (error) {
  } finally {
    tableLoading.value = false
  }
}

import type { FormInstance } from 'element-plus'
import { routerPush } from '@/utils/authutil'
const fromEl = ref<FormInstance>()
const search = () => {
  fromEl.value &&
    fromEl.value.validate(async (valid) => {
      if (!valid) {
        return
      }
      getList()
    })
}
const reset = (): void => {
  queryData.value.customerName = ''
  getList()
}
//查看卡产品订单信息
const handle = (e) => {
  routerPush({
    name: 'BatchCardDetail',
    query: { orderId: e.orderId }
  })
}
const cancel = (e) => {
  ElMessageBox.confirm('是否撤销订单?', '提示', {
    confirmButtonText: '是',
    cancelButtonText: '否',
    type: 'warning'
  })
    .then(async () => {
      const res = await cancelApi(e.orderId, e.orderType)
      res.code &&
        ElMessage({
          message: '订单撤销成功',
          type: 'success'
        })
      getList()
    })
    .catch(() => {})
}

//销售签审 跳转-编辑页面
const onSaleConfirm = async (e) => {
  try {
    goValetLoading.value = true
    await router.push({
      name: 'Valet',
      query: { orderId: e.orderId }
    })
    goValetLoading.value = false
  } finally {
  }
}

//跳转-代客下单
const goValetLoading = ref(false)
const goValet = async (param: string, row?: any) => {
  if (undefined === row) {
    row = { orderId: '' }
  }
  //跳转到代客下单页
  try {
    goValetLoading.value = true
    await router.push({ name: 'Valet', query: { orderId: row.orderId } })
    goValetLoading.value = false
  } finally {
  }
}
// 设置序号
const indexMethod = (index: number): number => {
  return (pagination.pageNo - 1) * pagination.pageSize + index + 1
}
const customerNameList = ref()
const getCustomerName = async () => {
  const res = await fastSearchApi()
  customerNameList.value = res
  //   if (res && Object.keys(res).length > 0 && queryData.customerId === '') {
  //     queryData.customerId = Object.keys(res)[0]
  //     getList()
  //   }
}
const orderStatusList = cloneDeep(getDictOptions('batch_order_status'))

Array.isArray(orderStatusList) && orderStatusList.unshift({ label: '全部', value: '' })

//显示客户类型名称
const displayIndustryName = (industry: string) => {
  const customerIndustry = industryListBatchCard.filter((item) => item.value == industry)[0]
  return customerIndustry?.label ?? '-'
}

// 查询条件配置
const queryOpts = ref<Record<string, QueryOption>>({
  customerName: {
    label: '客户名称',
    defaultVal: '',
    controlRender: (form: any) => (
      <CustomerSelect v-model:customer-name={form.customerName} isSearchByName />
    )
  },
  orderSource: {
    label: '订单来源',
    defaultVal: '',
    controlRender: (form: any) => (
      <el-select v-model={form.orderSource} clearable style="width: 100%; min-width: 100%">
        {Object.entries(orderSourceList).map(([key, value]) => (
          <el-option label={value} value={key} key={key} />
        ))}
      </el-select>
    )
  },
  orderCode: {
    label: 'UMV订单编号',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput v-model={form.orderCode} placeholder="请输入UMV订单编号" clearable />
    )
  },
  orderType: {
    label: '订单类型',
    defaultVal: '',
    controlRender: (form: any) => (
      <el-select v-model={form.orderType} clearable style="width: 100%; min-width: 100%">
        {orderTypeOptions.value.map((item) => (
          <el-option label={item.label} value={item.value} key={item.value} />
        ))}
      </el-select>
    )
  },
  orderStatus: {
    label: '订单状态',
    defaultVal: '',
    controlRender: (form: any) => (
      <el-select v-model={form.orderStatus} clearable style="width: 100%; min-width: 100%">
        {orderStatusList.map((item) => (
          <el-option label={item.label} value={item.value} key={item.value} />
        ))}
      </el-select>
    )
  },
  vname: {
    label: '产品名称',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.vname}
        placeholder="请输入产品名称"
        maxlength="40"
        clearable
      />
    )
  },
  createUserName: {
    label: '创建人',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.createUserName}
        placeholder="请输入创建人"
        maxlength="40"
        clearable
      />
    )
  },
  taskCode: {
    label: '任务编号',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.taskCode}
        placeholder="请输入任务编号"
        maxlength="40"
        clearable
      />
    )
  },
  customerIndustry: {
    label: '客户类型',
    defaultVal: '',
    controlRender: (form: any) => (
      <el-select v-model={form.customerIndustry} clearable style="width: 100%; min-width: 100%">
        {industryListBatchCard.map((item) => (
          <el-option label={item.label} value={item.value} key={item.value} />
        ))}
      </el-select>
    )
  },
  createOrderTime: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form: any) => (
      <el-date-picker
        v-model={form.createOrderTime}
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD HH:mm:ss"
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
        // value-format="YYYY-MM-DD"
        // default-time={[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]}
      />
    )
  },

  selfOnly: {
    label: ' ',
    defaultVal: false,
    controlRender: (form: any) => <el-checkbox v-model={form.selfOnly} label="只看自己" />
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'index', label: '序号', width: '100', type: 'index', index: indexMethod },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '110',
    renderTemplate: (scope) => (
      <span>{customerNameList.value && customerNameList.value[scope.row.customerId]}</span>
    )
  },

  {
    prop: 'customerIndustry',
    label: '客户类型呢',
    minWidth: '110',
    renderTemplate: (scope) => <span>{displayIndustryName(scope.row.customerIndustry)}</span>
  },

  {
    prop: 'orderCode',
    label: 'UMV订单编号',
    minWidth: '250',
    renderTemplate: (scope) => (
      <el-button
        class="operate"
        v-track:click={{ btn: true }}
        onClick={() => handle(scope.row)}
        link
      >
        {scope.row.orderCode}
      </el-button>
    )
  },
  { prop: 'taskCode', label: '任务编号', minWidth: '250', showOverflowTooltip: true },
  {
    prop: 'productionInfo',
    label: '产品名称',
    minWidth: '150',
    renderTemplate: (scope) => {
      if (scope.row?.orderDetailExt?.productionList?.length > 0) {
        return (
          <div>
            {scope.row?.orderDetailExt?.productionList.slice(0, 2).map((item, index) => (
              <div class="info-item flex-between" key={item.productionName}>
                <div class="card-warp flex-index">
                  <span class="production-name">{item.productionName || '-'}</span>
                </div>
              </div>
            ))}
            {scope.row?.orderDetailExt?.productionList?.length > 2 && <p>更多...</p>}
          </div>
        )
      }
      return <span>-</span>
    }
  },
  {
    prop: 'productTypeCount',
    label: '产品数量',
    minWidth: '130',
    renderTemplate: (scope) => <span>{scope.row.productTypeCount}</span>
  },
  {
    prop: 'orderTotalPrice',
    label: '价格',
    minWidth: '130',
    renderTemplate: (scope) => (
      <span>{formatMoneyDigitsEx(scope.row.orderTotalPrice, scope.row.orderExt.coinType)}</span>
    )
  },
  {
    prop: 'orderType',
    label: '订单类型',
    minWidth: '100',
    renderTemplate: (scope) => <span>{getDictLabel('order_type', scope.row.orderType)}</span>
  },
  {
    prop: 'orderSource',
    label: '来源',
    minWidth: '160',
    renderTemplate: (scope) => (
      <span>{scope.row?.orderSource ? orderSource[scope.row?.orderSource] : '-'}</span>
    )
  },
  { prop: 'createUserName', label: '创建人', minWidth: '160' },
  { prop: 'createTime', label: '创建时间', minWidth: '160' },
  { prop: 'updateTime', label: '更新时间', minWidth: '160' },
  {
    prop: 'orderStatus',
    label: '订单状态',
    minWidth: '80',
    renderTemplate: (scope) => (
      <span>{getDictLabel('batch_order_status', scope.row.orderStatus)}</span>
    )
  },
  {
    prop: 'operations',
    label: '操作',
    minWidth: '120',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {scope.row.orderSource === 'management' && scope.row.orderStatus === 'WAIT_SUBMIT' && (
          <el-button
            class="operate"
            v-track:click={{ btn: true }}
            onClick={() => goValet('edit', scope.row)}
            link
          >
            编辑
          </el-button>
        )}
        <el-button
          class="operate"
          v-track:click={{ btn: true }}
          onClick={() => handle(scope.row)}
          link
        >
          查看
        </el-button>
      </div>
    )
  }
])

onMounted(() => {
  if (queryData.value.customerId) {
    getList()
  }
  getCustomerName()
})

onActivated(() => {
  if (!tableLoading.value && queryData.value.customerId) {
    getList()
  }
})
</script>
<template>
  <UmvContent>
    <UmvQuery
      ref="fromEl"
      v-model="queryData"
      :opts="queryOpts"
      :rules="rules"
      :width-size="4"
      label-width="auto"
      @check="getList"
      @reset="reset"
    />

    <UmvTable v-loading="tableLoading" :data="tableData" :columns="columns" @refresh="getList">
      <template #tools>
        <ElButton type="primary" v-track:click.btn @click="goValet('add')" :loading="goValetLoading"
          >订单录入
        </ElButton>
      </template>

      <template #pagination>
        <Pagination
          v-model:page="pagination.pageNo"
          v-model:limit="pagination.pageSize"
          :total="total"
          @pagination="getList"
        />
      </template>
    </UmvTable>
  </UmvContent>
</template>

<style lang="less" scoped>
h2 {
  font-size: 18px;
  font-weight: bold;
}
.el-table__body {
  .el-button + .el-button {
    margin-left: 0;
  }
  .operate {
    color: #409eff;
    width: auto;
  }
}
.reset {
  color: #fff;
}

@import '@/styles/public.less';
</style>
<style lang="less">
.Order_DIYOrder_ElDialog {
  .el-dialog__header {
    border: none;
  }
  .el-dialog__body {
    padding-top: 0;
  }
}
.card-warp {
  margin-top: 10px;
  display: flex;
  align-items: center;
  &:first-of-type {
    margin-top: 0;
  }
}
.info-item {
  margin-bottom: 15px;
  &:last-of-type {
    margin-bottom: 0;
  }
}
.popover-img {
  width: 330px;
  height: auto;
}
:deep(.el-image__error) {
  font-size: 8px;
}
.flex-index {
  display: flex;
  align-items: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-img {
  width: 50px;
  height: auto;
  min-height: 31px;
  margin-right: 19px;
}
</style>
