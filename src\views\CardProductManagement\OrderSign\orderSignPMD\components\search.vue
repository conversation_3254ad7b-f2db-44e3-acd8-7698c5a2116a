<template>
  <ElForm label-position="right" :model="queryData" ref="formRef">
    <ElRow :gutter="20">
      <ElCol :span="4">
        <ElFormItem prop="orderCode">
          <!-- 搜索订单编号 -->
          <SearchWildcardInput
            v-model="queryData.orderCode"
            :placeholder="`${t('orderHandle.search')}订单编号`"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="customerName">
          <!-- 搜索PDM客户名称 -->
          <SearchWildcardInput
            v-model="queryData.customerName"
            :placeholder="`${t('orderHandle.search')}${t('orderHandle.customerName')}`"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="orderType">
          <!-- 订单标识 -->
          <ElSelect
            v-model="queryData.orderType"
            :placeholder="t('orderHandle.orderTypeName')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="item in orderTypeOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="cardCode">
          <!-- 卡款编号 -->
          <SearchWildcardInput
            v-model="queryData.cardCode"
            :placeholder="t('orderHandle.cardCode')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="cardName">
          <!-- 卡款名称 -->
          <SearchWildcardInput
            v-model="queryData.cardName"
            :placeholder="t('orderHandle.cardName')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="applicationType">
          <!-- 应用类型 -->
          <ElSelect
            v-model="queryData.applicationType"
            :placeholder="t('orderHandle.applicationTypeName')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="item in applicationTypeOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="chipAppearance">
          <!-- 芯片外观 -->
          <ElSelect
            v-model="queryData.chipAppearance"
            :placeholder="t('orderHandle.chipAppearanceName')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="item in chipAppearanceOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="chipColor">
          <!-- 芯片颜色 -->
          <ElSelect
            v-model="queryData.chipColor"
            :placeholder="t('orderHandle.chipColorName')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="item in chipColorOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="submitApprovalChip">
          <!-- 报款芯片 -->
          <SearchWildcardInput
            v-model="queryData.submitApprovalChip"
            :placeholder="t('orderHandle.submitApprovalChip')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="orderChip">
          <!-- 下单芯片 -->
          <SearchWildcardInput
            v-model="queryData.orderChip"
            :placeholder="t('orderHandle.orderChip')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="cardOrganization">
          <!-- 卡组织类型 -->
          <ElSelect
            v-model="queryData.cardOrganization"
            :placeholder="t('orderHandle.cardOrganizationName')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="item in cardOrganizationOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="moduleNo">
          <!-- 模块号 -->
          <SearchWildcardInput
            v-model="queryData.moduleNo"
            :placeholder="t('orderHandle.orderChip')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="personalAssessOpinionName">
          <!-- 个人化评估意见 -->
          <SearchWildcardInput
            v-model="queryData.personalAssessOpinionName"
            :placeholder="t('orderHandle.personalAssessOpinionName')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="personalProcessName">
          <!-- 个人化流程 -->
          <SearchWildcardInput
            v-model="queryData.personalProcessName"
            :placeholder="t('orderHandle.personalProcessName')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="projectName">
          <!-- 项目编码 -->
          <SearchWildcardInput
            v-model="queryData.projectName"
            :placeholder="t('orderHandle.projectToCode')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="isEtcApplication">
          <!-- ETC应用 -->
          <ElSelect
            v-model="queryData.isEtcApplication"
            :placeholder="t('orderHandle.isEtcApplication')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="(item, index) in confirmOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="customerServiceUserName">
          <!-- 客服人员 -->
          <SearchWildcardInput
            v-model="queryData.customerServiceUserName"
            :placeholder="t('orderHandle.customerServiceUserName')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="sampleMakeUserName">
          <!-- 稿样人员 -->
          <SearchWildcardInput
            v-model="queryData.sampleMakeUserName"
            :placeholder="t('orderHandle.sampleMakeUserName')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="pmUserName">
          <!-- PM人员 -->
          <SearchWildcardInput
            v-model="queryData.pmUserName"
            :placeholder="t('orderHandle.pmUserName')"
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="initiatTime">
          <!-- 开始时间,结束时间 -->
          <ElDatePicker
            v-model="queryData.initiatTime"
            type="daterange"
            value-format="YYYY-MM-DD HH:mm:ss"
            range-separator="-"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            :start-placeholder="t('orderHandle.startTime')"
            :end-placeholder="t('orderHandle.endTime')"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="reviewResult">
          <!-- 签审结果 -->
          <ElSelect
            v-model="queryData.reviewResult"
            :placeholder="t('orderHandle.reviewResult')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="item in reviewResultOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="primaryFlag">
          <!-- 主卡号 -->
          <ElSelect
            v-model="queryData.primaryFlag"
            :placeholder="t('orderHandle.primaryFlag')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="(item, index) in confirmOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4">
        <ElFormItem prop="pmLeaderUserName">
          <!-- 经理审核人员 -->
          <ElSelect
            v-model="queryData.pmLeaderUserName"
            :placeholder="t('orderHandle.managerHandler')"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="(item, index) in pmLeaderUserNameOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="4" class="btn-warp">
        <div class="checkbox-warp">
          <ElCheckbox
            v-model="queryData.selfOnly"
            class="mr-2px"
            label="只看自己"
            @change="changeSelf"
          />
          <ElCheckbox v-model="queryData.uncompleted" label="未完成" @change="changeCompleted" />
        </div>

        <ElButton type="primary" v-track:click.btn @click="search" class="ml-8px">{{
          t('orderHandle.query')
        }}</ElButton>
        <ElButton @click="reset()" class="reset">{{ t('orderHandle.reset') }}</ElButton>
      </ElCol>
    </ElRow>
  </ElForm>
</template>
<script setup lang="ts">
import { SearchWildcardInput } from '@/components/SearchWildcardInput'
import { useI18n } from '@/hooks/web/useI18n'
import { useOrderStoreWithOut } from '@/store/modules/order'
import {
  pmReviewListOrderTypes,
  pmReviewListCardOrganizations,
  pmReviewListOptionResultTypes,
  listUserByDeptCodes
} from '@/api/OrderHandle/pdmOrder'
const orderStore = useOrderStoreWithOut()

const { t } = useI18n()
interface CUSTOMVO {
  name: string
}
interface IqueryData {
  orderCode: string | undefined
  customerName: string | undefined
  orderType: string | undefined
  cardCode: string | undefined
  cardName: string | undefined
  applicationType: string | undefined
  chipAppearance: string | undefined
  chipColor: string | undefined
  submitApprovalChip: string | undefined
  orderChip: string | undefined
  cardOrganization: string | undefined
  orderModulesModuleChipModuleCode: string | undefined
  personalAssessOpinionName: string | undefined
  personalProcessName: string | undefined
  projectName: string | undefined
  isEtcApplication: string | undefined
  customerServiceUserName: string | undefined
  sampleMakeUserName: string | undefined
  pmUserName: string | undefined
  initiatTime: string | undefined
  reviewResult: string | number | undefined
  primaryFlag: string | undefined
  pmLeaderUserName: string | undefined
  moduleNo: string | undefined
  selfOnly: boolean | undefined
  uncompleted: boolean | undefined
  startTime: string | undefined
  endTime: string | undefined
}

const formRef = ref()

const emits = defineEmits(['search', 'reset', 'changeSelf', 'changeCompelete'])

const queryData: Ref<IqueryData> = ref({
  orderCode: undefined,
  customerName: undefined,
  orderType: undefined,
  cardCode: undefined,
  cardName: undefined,
  applicationType: undefined,
  chipAppearance: undefined,
  chipColor: undefined,
  submitApprovalChip: undefined,
  orderChip: undefined,
  cardOrganization: undefined,
  orderModulesModuleChipModuleCode: undefined,
  personalAssessOpinionName: undefined,
  personalProcessName: undefined,
  projectName: undefined,
  isEtcApplication: undefined,
  customerServiceUserName: undefined,
  sampleMakeUserName: undefined,
  pmUserName: undefined,
  initiatTime: undefined,
  reviewResult: undefined,
  primaryFlag: undefined,
  pmLeaderUserName: undefined,
  moduleNo: undefined,
  selfOnly: true,
  uncompleted: false,
  startTime: undefined,
  endTime: undefined
})

const originQueryData: Ref<IqueryData> = ref({
  orderCode: undefined,
  customerName: undefined,
  orderType: undefined,
  cardCode: undefined,
  cardName: undefined,
  applicationType: undefined,
  chipAppearance: undefined,
  chipColor: undefined,
  submitApprovalChip: undefined,
  orderChip: undefined,
  cardOrganization: undefined,
  orderModulesModuleChipModuleCode: undefined,
  personalAssessOpinionName: undefined,
  personalProcessName: undefined,
  projectName: undefined,
  isEtcApplication: undefined,
  customerServiceUserName: undefined,
  sampleMakeUserName: undefined,
  pmUserName: undefined,
  initiatTime: undefined,
  reviewResult: undefined,
  primaryFlag: undefined,
  pmLeaderUserName: undefined,
  moduleNo: undefined,
  startTime: undefined,
  endTime: undefined,

  selfOnly: true,
  uncompleted: false
})

const confirmOptions = ref([
  {
    label: '是',
    value: true
  },
  {
    label: '否',
    value: false
  }
])

const pmLeaderUserNameOptions = ref([])

const reviewResultOptions = ref([])

const orderTypeOptions = ref([])

const applicationTypeOptions = ref([])

const chipAppearanceOptions = ref([])

const chipColorOptions = ref([])

const cardOrganizationOptions = ref([])

// 获取签审卡款芯片外观列表
const getListChipAppearancesList = async () => {
  chipAppearanceOptions.value = await orderStore.getListChipAppearancesAction()
}

// 获取签审卡款芯片颜色列表
const getListChipColorsList = async () => {
  chipColorOptions.value = await orderStore.getListChipColorsDataAction()
}

// 获取订单标识
const pmReviewListOrderTypesList = async () => {
  orderTypeOptions.value = await pmReviewListOrderTypes()
}

// 获取应用类型
const pmReviewListApplicationTypesList = async () => {
  applicationTypeOptions.value = await orderStore.getPmReviewListApplicationTypes()
}

// 获取卡组织
const pmReviewListCardOrganizationsList = async () => {
  cardOrganizationOptions.value = await pmReviewListCardOrganizations()
}

// 获取签审结果
const pmReviewListOptionResultTypesList = async () => {
  reviewResultOptions.value = await pmReviewListOptionResultTypes()
}
// 加载PM经理下拉列表
const loadPmLeaderList = async () => {
  const pmUsers = await listUserByDeptCodes(['pm'])
  pmUsers.map((item) => {
    const leader = { label: item.nickname, value: item.nickname }
    pmLeaderUserNameOptions.value.push(leader)
  })
}

// 更改只看自己
const changeSelf = () => {
  emits('changeSelf')
}

const changeCompleted = () => {
  emits('changeCompelete')
}

import { deepClone } from '@/utils/deep'

const reset = () => {
  formRef.value.resetFields() // 初始化校验
  queryData.value = deepClone(originQueryData.value)
  console.log('queryData.value', queryData.value)
  emits('search')
}
const search = () => {
  emits('search')
}

watch(
  () => queryData.value.initiatTime,
  () => {
    console.log(queryData.value.initiatTime)

    try {
      if (queryData.value?.initiatTime) {
        queryData.value.startTime = queryData.value.initiatTime[0]
        queryData.value.endTime = queryData.value.initiatTime[1]
      } else {
        queryData.value.startTime = undefined
        queryData.value.endTime = undefined
      }
    } catch {}
  }
)

onMounted(() => {
  getListChipAppearancesList()
  getListChipColorsList()
  pmReviewListOrderTypesList()
  pmReviewListApplicationTypesList()
  pmReviewListCardOrganizationsList()
  pmReviewListOptionResultTypesList()
  loadPmLeaderList()
})

defineExpose({
  queryData
})
</script>
<style lang="less" scoped>
.btn-warp {
  display: flex;
  align-items: center;
  height: 32px;
}
.checkbox-warp {
  :deep(.el-checkbox) {
    height: 24px;
  }
}
</style>
