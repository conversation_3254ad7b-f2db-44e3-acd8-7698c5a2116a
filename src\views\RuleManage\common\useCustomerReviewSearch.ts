import { ref, onMounted, watch } from 'vue'
import { getCustomerList, getPageList } from '@/api/RuleManage'

// 主要的hook函数
export function useCustomerReviewSearch() {
  const customerId = ref('')
  const reviewNo = ref('')
  const reviewConfiglist = ref<any[]>([])
  const customerList = ref<any[]>([])
  const loading = ref(false)

  // 获取客户列表
  const getCustomers = async () => {
    try {
      loading.value = true
      const resData = await getCustomerList({ type: 2 })
      customerList.value = resData || []
    } catch (error) {
      console.error('获取客户列表失败:', error)
      customerList.value = []
    } finally {
      loading.value = false
    }
  }

  // 获取图审服务列表
  const getReviewServices = async (customerNo?: string) => {
    try {
      const data = {
        customerNo: customerNo || '', // 如果没有客户ID则传空字符串，获取所有图审服务
        reviewName: '',
        reviewNo: '',
        pageNum: '1',
        pageSize: '999999'
      }
      const resData = await getPageList(data)
      reviewConfiglist.value = resData?.list || []
    } catch (error) {
      console.error('获取图审服务列表失败:', error)
      reviewConfiglist.value = []
    }
  }

  // 客户变更处理
  const changeCusNo = async (value: string | undefined) => {
    customerId.value = value || ''
    // 清空图审选择
    reviewNo.value = ''

    // 重新获取图审列表
    await getReviewServices(customerId.value)
  }

  // 图审服务变更处理
  const changeReviewNo = (value: string | undefined) => {
    reviewNo.value = value || ''
  }

  // 初始化数据
  const initData = async () => {
    await getCustomers()
    // 初始化时获取图审服务列表，如果有客户ID则关联，没有则获取所有
    await getReviewServices(customerId.value)
  }

  // 监听客户ID变化，自动处理图审服务列表
  watch(customerId, async (newCustomerId, oldCustomerId) => {
    // 如果之前有值现在清空，或者客户发生变化，则重新获取图审服务列表
    if (oldCustomerId !== undefined && newCustomerId !== oldCustomerId) {
      // 清空图审选择
      reviewNo.value = ''
      // 重新获取图审列表
      await getReviewServices(newCustomerId)
    }
  })

  // 组件挂载时初始化数据
  onMounted(() => {
    initData()
  })

  return {
    customerId,
    reviewNo,
    reviewConfiglist,
    customerList,
    loading,
    changeCusNo,
    changeReviewNo,
    getCustomers,
    getReviewServices,
    initData
  }
}
