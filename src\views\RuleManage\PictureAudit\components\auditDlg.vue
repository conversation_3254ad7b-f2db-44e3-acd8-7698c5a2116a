<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <div class="audit-dlg">
    <el-dialog
      width="95%"
      v-model="orderAudit"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <template #header>
        <div slot:header>
          <div class="flex" style="align-items: center">
            <span class="font-w600 font-22 mr-20px">审核</span>
          </div>

          <!-- <span class="margin-l-18 font-w600">项目名称：</span>
          {{ formData.itemName }} -->
          <!-- <span class="margin-l-5 font-w600" style="color: red"
            >（剩余待审：{{ formData.residueAuditCount }}）</span
          > -->
        </div>
      </template>
      <div class="container-box" v-loading="loading">
        <template v-if="isShow">
          <div class="flex flex-1">
            <div class="text-sign">{{ markHitFeatureInfoListHandle }}</div>
            <!-- <enlargeImage :imageList="formData.auditImage" /> -->
            <enlargeImageNew :list="formData.auditImage" class="flex-1" />
          </div>

          <div class="right-box">
            <div>
              <span class="font-w600">图审服务： </span><span>{{ formData.reviewName }}</span></div
            >
            <div>
              <span class="font-w600">任务编号：</span><span>{{ formData.auditNo }}</span></div
            >
            <div>
              <span class="font-w600">调用方ID：</span><span>{{ formData.orderNo }}</span></div
            >
            <div class="text-xl mt-5" v-if="formData.signature"
              >文字签名：{{ formData.signature }}</div
            >
            <div style="text-align: left; margin-top: 10px">
              <el-button type="primary" @click="openDialog('feature')">添加特征库</el-button>
              <el-button type="primary" @click="openDialog('similitude')">相似图</el-button>
              <el-button type="primary" @click="openDialog('ocr')">ocr识别</el-button>
              <ButtonPromise
                type="primary"
                :clickPromise="searchBtn"
                v-if="formData.searchRpaType == '1'"
                >搜图</ButtonPromise
              >
              <el-button type="primary" disabled v-if="formData.searchRpaType == '2'"
                >已搜图</el-button
              >
              <el-button
                type="primary"
                @click="openDialog('webSearch')"
                v-if="formData.searchRpaType == '3'"
                >搜索结果</el-button
              >
              <ButtonPromise
                type="primary"
                :clickPromise="searchBtn"
                v-if="formData.searchRpaType == '4'"
                >搜图失败,重新搜图</ButtonPromise
              >
              <!-- <el-button type="primary" @click="openDialog('webSearch')">网络搜索</el-button> -->
            </div>
            <TheTitleBOx title="审核要求"
              ><div v-if="auditClaim" v-html="auditClaim" class="audit-require"></div>
              <div v-else class="no-data">暂无数据</div></TheTitleBOx
            >
            <TheTitleBOx title="AI一审结果">
              <TheTag :list="diyOrderAisTaps" />
            </TheTitleBOx>

            <TheTitleBOx title="AI二审结果">
              <div
                v-for="item in diyOrderYiDunAisTaps"
                :key="item.categoryTitle"
                class="yidun-ai-list"
              >
                <el-divider class="yidun-ai-category" content-position="center">{{
                  item.categoryTitle
                }}</el-divider>
                <TheTag :list="item.list" />
              </div>
            </TheTitleBOx>
            <TheTitleBOx title="AI三审结果">
              <div class="yidun-ai-list">
                <el-divider class="yidun-ai-category" content-position="center">
                  自研大模型图审结果
                </el-divider>
                <TheTag :list="auditResult" />
                <el-divider class="yidun-ai-category" content-position="center">
                  自研大模型分类结果
                </el-divider>
                <TheTag :list="categoryResult" />
              </div>
            </TheTitleBOx>
            <TheTitleBOx title="人工审核">
              <el-row>
                <el-col :span="24" class="flex align-top">
                  <el-checkbox-group v-model="form.illegal">
                    <el-checkbox
                      v-for="(item, ind) in ruleNames"
                      :key="ind"
                      :value="item.reviewConfigArtId"
                      @change="(val) => changeIllegal(item, val)"
                      >{{ item.showReason }}</el-checkbox
                    >
                    <div class="flex items-start">
                      <el-checkbox value="其他原因" :disabled="form.illegal.includes('未使用AIGC')"
                        >其他原因</el-checkbox
                      >
                      <el-input
                        type="textarea"
                        :disabled="form.illegal.includes('未使用AIGC')"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        style="width: 200px; margin-left: 20px"
                        v-model="form.failReason"
                        autocomplete="off"
                        placeholder="请输入其他原因"
                        :rows="2"
                        maxlength="40"
                        show-word-limit
                        @blur="failReasonBlur"
                      />
                    </div>
                  </el-checkbox-group>
                </el-col>

                <el-col :span="24" style="margin-top: 10px">
                  <el-button
                    type="danger"
                    size="large"
                    style="font-size: 20px; font-weight: 600"
                    @click="handleOrderInfo('refuse')"
                    >拒 绝</el-button
                  >
                  <el-button
                    v-if="formData.doubt == true && auditStatus != '9999'"
                    type="info"
                    size="large"
                    style="font-size: 20px; font-weight: 600"
                    @click="handleOrderInfo('doubt')"
                    >存 疑</el-button
                  >
                  <el-button
                    type="danger"
                    size="large"
                    style="font-size: 20px; font-weight: 600"
                    v-if="formData.specialApproval == 1"
                    @click="onSpecialApproval"
                    >特批</el-button
                  >
                  <el-button
                    v-if="
                      (formData.specialApproval || formData.orderExpedited) &&
                      formData.orderExpedited == 1
                    "
                    type="danger"
                    size="large"
                    style="font-size: 20px; font-weight: 600"
                    @click="onOrderExpedited"
                  >
                    加急
                  </el-button>
                </el-col>
              </el-row>
            </TheTitleBOx>
            <div class="yidun-ai-list">
              <el-divider class="yidun-ai-category" content-position="center" />
            </div>
          </div>
        </template>
      </div>

      <!-- 按钮 -->
      <div slot:footer class="dialog-footer text-r">
        <el-button
          type="primary"
          class="font-16 font-w600"
          style="margin-left: 60px"
          @click="StepHandle('last')"
          :loading="loading"
          :disabled="!lastBtn"
          >上一条</el-button
        >
        <el-button
          type="primary"
          class="font-16 font-w600"
          style="margin-left: 60px"
          @click="StepHandle('next')"
          :loading="loading"
          :disabled="!nextBtn"
          >下一条</el-button
        >
        <el-button
          type="success"
          class="font-20 font-w600"
          style="margin-left: 60px; height: 45px"
          @click="handleOrderInfo('pass')"
          :loading="loading"
          :disabled="form.illegal.length > 0 || !isShow"
          >通 过</el-button
        >
        <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
      </div>
      <div class="extendInfo">
        <p v-for="(item, index) in extendInfo" :key="index">{{ item.key }} : {{ item.value }}</p>
      </div>
    </el-dialog>
    <!-- 删除弹窗 -->
    <ocrDlg
      v-if="ocrRule"
      :ocrRule="ocrRule"
      :dataInfo="orderInfo"
      :orderId="currentOrderId"
      @closeDialog="closeDialog"
    />
    <webSearchDlg v-model="webSearchRule" :image-urls="formData.searchRPAList" />
    <similitudeDlg
      v-if="similitudeRule"
      :similitudeRule="similitudeRule"
      :dataInfo="orderInfo"
      :orderId="currentOrderId"
      @closeDialog="closeDialog"
    />
    <!-- 查看 -->
    <FilePreviewDialog v-model="showFilePreviewDialog" :editMode="false" :fileList="fileList">
      <!-- <template #infoContent>
        <p style="padding-bottom: 20px">备注信息：{{ formData.noteStr }}</p>
      </template> -->
    </FilePreviewDialog>
  </div>
  <!-- <el-dialog
    v-model="markTipsDlgFlag"
    title="标记提示"
    :close-on-click-modal="false"
    width="800px"
    :destroy-on-close="true"
    top="2vh"
  >
  </el-dialog> -->
  <DialogFrom ref="dialogFrom" />
</template>
<script setup lang="ts">
const { t } = useI18n()
// import markTipsDlg from './markTipsDlg.vue'
import { getMarkTipsaApi } from '@/api/RuleManage/MarkGallery'
import { ref, toRefs, reactive, computed, onMounted } from 'vue'
import ocrDlg from './ocrDlg.vue'
import webSearchDlg from './webSearchDlg.vue'
import similitudeDlg from './similitudeDlg.vue'
import TheTitleBOx from './TheTitleBox.vue'
import TheTag from './TheTag.vue'
import { getOrderInfoApi, handleOrderInfoApi, tipNoteApi } from '@/api/RuleManage/PictureAudit'
import useMessage from '@/utils/useMessage'
import '@/views/RuleManage/common/css/index.css'
// import enlargeImage from './TheImageBox.vue'
import enlargeImageNew from './TheImageBoxNew.vue'
import FilePreviewDialog from '@/views/CardProductManagement/OrderSign/PlmOrder/Task/Detail/components/FilePreviewDialogNew.vue' // 文件预览弹窗

const message = useMessage()

/** 接收父级传入的参数 start */
const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => {}, //默认值
    required: false //是否必传
  },
  auditStatus: {
    type: String,
    default: ''
  },
  nextBtn: {
    type: Boolean,
    required: true //是否必传
  },
  lastBtn: {
    type: Boolean,
    required: true //是否必传
  },
  nextStep: {
    type: Function,
    default: async () => {}
  },
  lastStep: {
    type: Function,
    default: async () => {}
  }
})
// 解构传入的弹窗开关
const { orderInfo, auditStatus } = toRefs(props)
const orderAudit = defineModel<boolean>()

const loading = ref(true)

const ocrRule = ref(false) // ocr弹窗
const webSearchRule = ref(false) // web弹窗
const similitudeRule = ref(false) // web弹窗
const auditRequireRule = ref(false) // 审核内容弹窗

const diyOrderAis = ref([]) // AI审核结果
const diyOrderYiDunAis = ref([]) // 网易AI审核结果
const diyOrderAisTaps = computed(() =>
  diyOrderAis.value.map((item) => ({
    title: item.showReason,
    type: item.aiAuditLegal == '1' ? ('success' as const) : ('fail' as const)
  }))
)
const diyOrderYiDunAisTaps = computed(() => {
  return diyOrderYiDunAis.value.map((item) => {
    return {
      categoryTitle: item.desc,
      list:
        item?.yiDunLabelInfoList?.map((item) => {
          return {
            title: item.ruleName,
            type:
              item.aiAuditLegal == '1'
                ? ('success' as const)
                : item.aiAuditLegal == '2'
                ? 'warning'
                : 'fail'
          }
        }) ?? []
    }
  })
})
type ResultType = { title: string; type: 'success' | 'warning' | 'fail' | 'show'; remark: string }
const auditResult = ref<ResultType[]>([])
function getAuditResult(llmResultList): ResultType[] {
  if (!llmResultList) {
    return []
  }

  const obj = llmResultList.find((item) => item.category == 'audit_result')
  if (!obj) {
    return []
  }
  const auditResultObj = obj.llmResultInfo
  const isOK = auditResultObj.aiAuditLegal == 1
  return [
    {
      title: `图审结果：${isOK ? '通过' : '不通过。原因：' + auditResultObj.aiAuditMsg}`,
      type: isOK ? 'success' : 'fail',
      remark: auditResultObj.showReason
    }
  ]
}
const categoryResult = ref<ResultType[]>([])
function getCategoryResult(llmResultList): ResultType[] {
  if (!llmResultList) {
    return []
  }
  const obj = llmResultList.find((item) => item.category == 'category_result')
  if (!obj) {
    return []
  }
  const llmResultInfo = obj.llmResultInfo
  const llmImageCategoryList = llmResultInfo.llmImageCategoryList
  return llmImageCategoryList.map((item) => ({
    title: item.labelName,
    type: 'show',
    remark: llmResultInfo.showReason
  }))
}

const extendInfo = ref([]) //图审平台字段
interface ArtRule {
  reviewConfigArtId: string
  reviewNo: string
  auditServerNo: string
  showReason: string
  used: number
  ruleSort: number
}

const ruleNames = ref<ArtRule[]>([]) // 图审平台人工审核字段
const formDataOrigin = () => {
  return {
    auditImage: [] as { image: string; title: string }[], // 审核图片
    auditNo: '', // 审核编号
    reviewName: '', // 图审服务
    itemName: '', // 项目名称
    specialApproval: 0, // 是否特批 0:否 1是
    isQues: 0, // 审核存疑按钮 // 0否1是
    residueAuditCount: 0, // 剩余审核数量
    orderExpedited: 0, // 是否加急，0否1是
    doubt: false, // 存疑按钮是否展示
    version: '',
    reviewNo: '',
    signature: '', //文字签名
    specialApprovalAttachment: [] as any[], //特批附件列表
    orderExpeditedAttachment: [], //加急附件列表
    fileList: [],
    urgentNote: '', //加急图审备注
    specialNote: '', //特批图审备注
    noteStr: '',
    searchRpaType: '', //搜图状态
    searchRPAList: [], //搜图返回的图片数组
    orderNo: '' // 调用方ID
  }
}
const formData = reactive(formDataOrigin())
const form = reactive({
  failReason: '', // 其他原因
  illegal: [] as string[]
})

watch(
  () => orderInfo?.value?.orderId,
  (value) => {
    if (!value) {
      return
    }
    nextTick(() => {
      getFormData()
    })
  },
  {
    immediate: true
  }
)

const currentOrderId = computed(() => orderInfo?.value?.orderId) // 当前审核的id

// 获取审核数据
const isShow = ref(true)
let cardImage = ''
async function getFormData() {
  loading.value = true
  isShow.value = true
  // 标记提示弹窗
  // await getMarkTipsInfo()

  const params = {
    orderId: currentOrderId.value
  }
  try {
    const resData = await getOrderInfoApi(params)
    await getReviewAuditNote()

    formData.residueAuditCount = resData.residueAuditCount || 0 // 剩余审核数量
    formData.auditNo = resData.auditNo // 任务编号
    formData.version = resData.version
    formData.reviewNo = resData.reviewNo // 审核编号
    formData.signature = resData.signature // 文字签名
    formData.searchRPAList = resData.searchRPAList
    formData.searchRpaType = resData.searchRpaType
    formData.orderNo = resData.orderNo // 调用方ID
    // 审核图和制卡图
    formData.auditImage = []
    if (resData.auditImage) {
      formData.auditImage.push({ image: resData.auditImage, title: '预览图' })
    }
    if (resData.cardImage) {
      formData.auditImage.push({ image: resData.cardImage, title: '制卡图' })
      cardImage = resData.cardImage
    }
    if (resData.useAigc == '1') {
      formData.auditImage.push({ image: resData.originalImage, title: 'AI前的原图' })
    }
    if (resData.reviewNo) {
      await getTipNote()
    }
    diyOrderAis.value = resData.approveAiInfos || [] // AI审核结果
    diyOrderYiDunAis.value = resData.yiDunCategoryList || [] // 网易AI审核结果
    formData.specialApproval = resData.specialApproval // 是否特批
    formData.orderExpedited = resData.orderExpedited // 是否加急

    auditResult.value = getAuditResult(resData?.llmResultList)
    categoryResult.value = getCategoryResult(resData?.llmResultList)
    // 人工审核规则列表
    const initRules = resData.artRuleInfos || []
    const AIGC_RULE = {
      reviewConfigArtId: '未使用AIGC',
      reviewNo: '8B06921BF9A79999',
      auditServerNo: '999999',
      showReason: '未使用AIGC',
      used: 1,
      ruleSort: 99
    } // 20250331兴业银行新增人工审核项， 未使用AIGC
    ruleNames.value = [...initRules, ...(Boolean(resData.needUsageAigc) ? [AIGC_RULE] : [])]
    formData.doubt = resData.doubt // 存疑按钮是否展示
    formData.specialApprovalAttachment = resData.specialApprovalAttachment
    formData.orderExpeditedAttachment = resData.orderExpeditedAttachment
    formData.urgentNote = resData.urgentNote
    formData.specialNote = resData.specialNote
    const markInfo = resData?.markInfo
    markSpecialInfo.value = markInfo?.specialInfo
    markHitFeatureInfoList.value = markInfo?.markHitFeatureInfoList
    form.illegal = []
    form.failReason = ''
    //若是存疑页面，则添加人工审核原因
    if (props.auditStatus == '9999') {
      form.failReason = resData.artRefuseReason
      form.illegal = resData.configArtId || []
      failReasonBlur()
    }
  } catch (error) {
    // const obj = formDataOrigin()
    // Object.keys(obj).forEach((item) => {
    //   formData[item] = obj[item]
    // })
    console.log('获取审核详情数据失败！' + error)
    isShow.value = false
  } finally {
    loading.value = false
    formData.reviewName = orderInfo.value?.reviewName // 图审服务
    formData.itemName = orderInfo.value?.itemName // 项目名称
  }
}

const getTipNote = async () => {
  if (!formData.reviewNo) return
  const params = {
    reviewNo: formData.reviewNo
  }
  const resData = await tipNoteApi(params)
  if (resData.data == '') {
    auditRequireRule.value = false
  } else {
    auditRequireRule.value = true
  }
}
// 拒绝、存疑、通过
const handleOrderInfo = async (type) => {
  if (!type) return
  const orderId = currentOrderId.value
  // 拒绝时，要勾选原因
  if ((type == 'refuse' || type == 'doubt') && !form.illegal.length) {
    message.error('请勾选人工审核项！')
    return
  }
  // 拒绝时，如果勾选了其他原因，则必填
  if (
    (type == 'refuse' || type == 'doubt') &&
    form.illegal.includes('其他原因') &&
    !form.failReason
  ) {
    message.error('请填写其他原因！')
    return
  }

  if (
    (type == 'refuse' || type == 'doubt' || type == 'pass') &&
    !form.illegal.includes('未使用AIGC') &&
    ruleNames.value.find((item) => item.reviewConfigArtId == '未使用AIGC')
  ) {
    try {
      await ElMessageBox.confirm(
        '检测到动漫/单人，是否勾选“未使用AIGC”',
        t('common.confirmTitle'),
        {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning',
          distinguishCancelAndClose: true
        }
      )
      form.illegal = []
      form.illegal.push('未使用AIGC')
      form.failReason = '无法确定版权'
      return
    } catch (error) {
      if (error == 'cancel') {
      } else {
        return
      }
    }
  }

  if (type == 'refuse' && formData.specialApproval == 1) {
    try {
      await ElMessageBox.confirm('该图申请特放，请确认', t('common.confirmTitle'), {
        confirmButtonText: '拒绝',
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
    } catch (error) {
      return
    }
  }
  // if (type == 'refuse' && markList.value.length > 0) {
  //   await ElMessageBox.confirm(`检测到图片被标记为：特放，是否还是拒绝？`, '提示', {
  //     confirmButtonText: '确认',
  //     cancelButtonText: '取消',
  //     type: 'warning',
  //     distinguishCancelAndClose: true
  //   })
  // }
  // if (type == 'pass' && markList.value.length > 0) {
  //   await ElMessageBox.confirm(
  //     `检测到图片被标记为：${markList.value.join('，')}，是否确认通过？`,
  //     '提示',
  //     {
  //       confirmButtonText: '确认',
  //       cancelButtonText: '取消',
  //       type: 'warning',
  //       distinguishCancelAndClose: true
  //     }
  //   )
  // }
  loading.value = true
  try {
    const reviewConfigArtIds = form.illegal.filter(
      (item) => item != '其他原因' && item != '未使用AIGC'
    ) // 勾选的拒绝id[]
    let failReason = form.failReason
    if (form.illegal.includes('未使用AIGC')) {
      failReason = '无法确定版权'
    }

    const params = {
      orderId,
      auditResult: type,
      reviewConfigArtIds, // 拒绝人工审核项Id
      approveSuggest: failReason, // 人工审批意见
      version: formData.version,
      approveNode: auditStatus.value,
      usageAigcHandle: form.illegal.includes('未使用AIGC') ? true : false
    }
    console.log(params, 'params')
    await handleOrderInfoApi(params)
    StepHandle('next')
  } catch (error) {
    message.error('操作失败!' + error)
  } finally {
    loading.value = false
  }
}
async function StepHandle(type: 'next' | 'last') {
  loading.value = true
  try {
    if (type == 'next') {
      await props.nextStep()
    } else {
      await props.lastStep()
    }
  } catch (err) {
  } finally {
    loading.value = false
  }
}

const openDialog = (type) => {
  switch (type) {
    case 'webSearch':
      webSearchRule.value = true
      break
    // 编辑
    case 'ocr':
      // dataInfo.value = obj
      ocrRule.value = true
      break
    // 删除
    case 'similitude':
      // dataInfo.value = obj
      similitudeRule.value = true
      break
    case 'auditRequire':
      auditRequireRule.value = true
      break
    case 'feature':
      console.log('feature')
      feature()
      break
  }
}
// 关闭添加角色弹窗
const closeDialog = (result) => {
  ocrRule.value = false
  webSearchRule.value = false
  similitudeRule.value = false
  auditRequireRule.value = false
  // if (result) {
  //   queryCategoryList()
  // }
}
// 关闭删除角色弹窗
const handleClose = () => {
  orderAudit.value = false
}
// 标记提示相关
// const markTipsDlgFlag = ref(false)

// const markTipsInfo = reactive({
//   markId: '',
//   markType: '',
//   markDesc: '',
//   markImage: ''
// })

// // 先获取标记提示数据，如果接口报错就不进行弹窗
// const getMarkTipsInfo = async () => {
//   if (!currentOrderId.value) {
//     return
//   }
//   try {
//     const params = {
//       orderId: currentOrderId.value
//     }
//     const resData = await getMarkTipsaApi(params)
//     markTipsInfo.markId = resData?.markId
//     markTipsInfo.markType = resData?.markType
//     markTipsInfo.markDesc = resData?.markDesc
//     markTipsInfo.markImage = resData?.markImage
//     if (resData || resData.markId || resData.markType || resData.markDesc) {
//       markTipsDlgFlag.value = true
//     } else {
//       markTipsDlgFlag.value = false
//     }
//   } catch (error) {
//     markTipsDlgFlag.value = false
//     console.error('标记提示接口error，不弹窗: ' + error)
//   } finally {
//   }
//   markTipsDlgFlag.value = true
// }
function failReasonBlur() {
  if (form.failReason && !form.illegal.includes('其他原因')) {
    form.illegal.push('其他原因')
    return
  }
  if (!form.failReason && form.illegal.includes('其他原因')) {
    form.illegal = form.illegal.filter((item) => item != '其他原因')
    return
  }
}
//获取审核要求
const auditClaim = ref('')
import { getReviewAuditNoteApi, searchRPAApi } from '@/api/RuleManage/PictureAudit'

const getReviewAuditNote = async () => {
  try {
    const resData = await getReviewAuditNoteApi({
      reviewNo: orderInfo.value.reviewNo
    })
    auditClaim.value = resData.reviewNote
  } catch (error) {
    message.error('获取数据失败！' + error)
  }
}
interface FileType {
  eosUrl: any
  fileName: string
  saveFileName: string
}
type fileListType = {
  tip: string
  file: FileType
  title: '订单特批信息' | '标记特批信息'
}

const showFilePreviewDialog = ref(false)
const fileList = ref<fileListType[]>([])
const markSpecialInfo = ref<any>()
function onSpecialApproval() {
  fileList.value = []
  const obj = {
    file: formData.specialApprovalAttachment?.[0],
    tip: formData.specialNote,
    title: '订单特批信息' as const
  }
  if (formData.specialApprovalAttachment?.length > 0 || formData.specialNote) {
    fileList.value.push(obj)
  }
  if (markSpecialInfo.value?.featureLabelCodeList.length > 0) {
    fileList.value.push({
      tip: markSpecialInfo.value?.specialRemark,
      file: {
        eosUrl: markSpecialInfo.value?.specialAttachedFileUrl,
        fileName: markSpecialInfo.value?.specialAttachedFileName,
        saveFileName: markSpecialInfo.value?.specialAttachedFileName
      },
      title: '标记特批信息'
    })
  }
  showFilePreviewDialog.value = true
  //console.log('显示特批')
}

const markHitFeatureInfoList = ref<any>([])

const markHitFeatureInfoListHandle = computed(() => {
  const list = markHitFeatureInfoList.value
    ?.filter((item) => item.labelCode !== 'SPECIAL')
    .map((item) => item.labelName)

  return list?.length > 0 ? '标记命中【' + list?.join('、 ') + '】' : ''
})

function onOrderExpedited() {
  fileList.value = []
  const obj = {
    file: formData.orderExpeditedAttachment?.[0],
    tip: formData.urgentNote
  }
  if (formData.orderExpeditedAttachment?.length > 0 || formData.urgentNote) {
    fileList.value.push(obj)
  }

  showFilePreviewDialog.value = true
  //console.log('显示加急')
}

async function searchBtn() {
  await searchRPAApi(currentOrderId.value)
  getFormData()
}

function changeIllegal(item, checked) {
  console.log('当前操作项:', item, '选中状态:', checked)
  if (item.reviewConfigArtId == '未使用AIGC') {
    if (checked) {
      form.failReason = '无法确定版权'
      nextTick(() => {
        form.illegal = form.illegal.filter((item) => item != '其他原因')
      })
    } else {
      if (form.failReason == '无法确定版权') {
        form.failReason = ''
      }
    }
  }
}
const dialogFrom = ref()

import { addMarkListApi } from '@/api/RuleManage/MarkGallery'
import { option } from '@/components/dialogFrom/index.vue'
import { getStrDictOptions } from '@/utils/dict'
import { title } from 'process'
async function feature() {
  const optionObj: option = {
    title: '添加特征库',
    formData: { featureLabelCodeList: [] },
    formList: [
      {
        type: 'select',
        key: 'featureLabelCodeList',
        label: '图片特征库标签',
        optionList: getStrDictOptions('feature_label'),
        itemProps: { multiple: true }
      }
    ],
    confirm: async (data) => {
      const result = await addMarkListApi({
        reviewNo: formData.reviewNo,
        markImage: cardImage,
        featureLabelCodeList: data.featureLabelCodeList
      })
      if (result) {
        ElMessage.success('标记成功')
      }
    }
  }
  dialogFrom.value.open(optionObj)
}
//标记图库展示
const markList = ref(['重复图片'])
</script>
<style scoped lang="less">
.audit-dlg {
  box-sizing: border-box;

  :deep(.el-dialog) {
    margin-top: 2vh !important;
    min-width: 1200px !important;
  }
  :deep(.el-dialog__header) {
    padding-bottom: 0;
  }
  .margin-l-18 {
    margin-left: 18px !important;
  }
  :deep(.el-dialog__body) {
    padding: 0 20px 0px;
  }
  :deep(.el-carousel__indicator.is-active button) {
    background: #409eff;
    color: #fff;
  }
  .btn-box {
    margin: 10px 10px 0 0;
    display: inline-block;
    padding: 3px 8px;
    font-size: 12px;
    // border: 1px solid red;
    border-radius: 3px;
    color: #fff;
    background: #e60012;
  }
  .border-color {
    background: #67c23a;
  }
  .audit-require {
    margin: 5px 0;
    padding: 10px 10px;
    width: 100%;
    min-height: 60px;
    max-height: 120px;
    background: #ffffff;
    border: 1px solid #eff0f2;
    border-radius: 8px;
    box-sizing: border-box;
    word-wrap: break-word;
    white-space: normal;
    // overflow: hidden;
    overflow: auto;
  }
}
.extendInfo {
  padding-left: 60px;
  width: 520px;
  max-height: 80px;
  overflow-y: auto;
}
.ai-result {
  overflow: hidden;
  font-size: 16px;
}
.ai-result.ai {
  min-height: 160px;
}

.ai-result {
  .el-form-item {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    line-height: 30px;
  }
}

:deep(.el-dialog.first-audit-dlg) {
  margin-top: 5vh !important;
  min-width: 1100px;
}
.container-box {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 500px;
}
.right-box {
  margin-top: 16px;
  min-width: 500px;
  width: 600px;
  font-size: 16px;
  .audit-require {
    font-size: 14px;
    padding: 5px;
    height: 100%;
    max-height: 120px;
    margin-top: 0;
  }
}

.yidun-ai-list {
  display: flex;
  flex-wrap: wrap;

  :deep(.yidun-ai-category) {
    flex-shrink: 0;
    margin: 20px 0 10px 0;
    .el-divider__text {
      font-size: 14px;
      font-weight: bold;
    }
  }
}
.mark-box {
  font-size: 32px;
  font-weight: bold;
  color: red;
}
.text-sign {
  padding-right: 40px;
  font-size: 30px;
  font-weight: bold;
  color: red;
}
.flex-1 {
  flex: 1;
}
</style>
