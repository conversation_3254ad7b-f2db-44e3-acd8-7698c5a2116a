<template>
  <UmvContent>
    <!-- 查询 -->
    <UmvQuery
      v-model="queryForm"
      :opts="queryOpts"
      label-width="120px"
      @check="handleSearch"
      @reset="handleReset"
    />

    <!-- 列表 -->
    <UmvTable v-loading="loading" :data="tableList" :columns="columns" @refresh="getTableList">
      <!-- 自定义工具栏 -->
      <template #tools>
        <el-button type="primary" size="small" v-track:click.btn @click="addEditBtn()">
          新增标记
        </el-button>
        <el-button type="primary" size="small" v-track:click.btn @click="addEditSpecial()">
          新增特批
        </el-button>
      </template>

      <!-- 分页器 -->
      <template #pagination>
        <Pagination
          :current-page="queryPage.pageNum"
          :page-size="queryPage.pageSize"
          :total="queryPage.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </template>
    </UmvTable>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dlgVisable"
      :title="dlgType == 'NewAdd' ? '新增标记' : '编辑标记'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="800px"
      :destroy-on-close="true"
    >
      <NewAddOrEdit
        :markTypeList="markTypeList"
        :markDescList="markDescList"
        :dlgInfo="dlgInfo"
        :dlgType="dlgType"
        @close-dialog="closeDialog"
      />
    </el-dialog>

    <DialogFrom ref="dialogFrom" />
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'MarkGallery'
})

const { t } = useI18n()
import NewAddOrEdit from './components/NewAddOrEdit.vue'
import { getMarkListApi, deleteMarkItemApiNew } from '@/api/RuleManage/MarkGallery'
import { ElMessageBox, ElImage, ElButton, ElTag, ElSelect, ElOption } from 'element-plus'

import UmvContent from '@/components/UmvContent'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { CustomerSelect } from '@/components/CustomerSelect/index'
import { useCustomerReviewSearch } from '@/views/RuleManage/common/useCustomerReviewSearch'

const message = useMessage()

// 使用客户和图审服务选择功能
const { customerId, reviewNo, reviewConfiglist, changeCusNo, changeReviewNo } =
  useCustomerReviewSearch()

onMounted(() => {
  getTableList()
})

// 查询表单数据
const queryForm = ref({
  customerId: '',
  reviewNo: '',
  featureLabelCodeList: []
})

// 监听客户和图审服务的变化，同步到查询表单
watch(
  [customerId, reviewNo],
  ([newCustomerId, newReviewNo]) => {
    queryForm.value.customerId = newCustomerId
    queryForm.value.reviewNo = newReviewNo
  },
  { immediate: true }
)

// 获取字典数据
import { getDictOptions } from '@/utils/dict'
const dictList = computed(() => {
  const list = [...getDictOptions('feature_label'), ...getDictOptions('feature_label_special')]
  return list
})

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  customerNo: {
    label: '客户',
    defaultVal: '',
    controlRender: (form: any) => (
      <CustomerSelect
        v-model={customerId.value}
        clearable
        filterable
        placeholder="请选择客户"
        style="width: 100%"
        onChange={changeCusNo}
      />
    )
  },
  reviewNo: {
    label: '图审服务',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect
        v-model={reviewNo.value}
        clearable
        filterable
        placeholder="请选择图审服务"
        style="width: 100%"
        onChange={changeReviewNo}
      >
        {reviewConfiglist.value.map((item) => (
          <ElOption key={item.reviewNo} label={item.reviewName} value={item.reviewNo} />
        ))}
      </ElSelect>
    )
  },
  featureLabelCodeList: {
    label: '图片标记标签',
    defaultVal: [],
    controlRender: (form: any) => (
      <ElSelect
        v-model={form.featureLabelCodeList}
        multiple
        clearable
        placeholder="请选择标签"
        style="width: 100%"
      >
        {dictList.value.map((dict) => (
          <ElOption key={dict.value} label={dict.label} value={dict.value} />
        ))}
      </ElSelect>
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '80px',
    renderTemplate: (scope) => (
      <span>{scope.$index + 1 + (queryPage.pageNum - 1) * queryPage.pageSize}</span>
    )
  },
  {
    prop: 'previewImage',
    label: '标记图',
    minWidth: '150px',
    renderTemplate: (scope) => (
      <ElImage
        v-show={scope.row.previewImage}
        fit="scale-down"
        src={scope.row.previewImage}
        z-index={100}
        preview-teleported={true}
        preview-src-list={[scope.row.markImage]}
      />
    )
  },
  {
    prop: 'featureLabelNameList',
    label: '标签',
    minWidth: '150px'
  },
  {
    prop: 'reviewName',
    label: '来源图审服务',
    minWidth: '150px'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: '150px'
  },
  {
    prop: 'customerName',
    label: '客户',
    minWidth: '150px'
  },
  {
    prop: 'specialRemark',
    label: '特批说明',
    minWidth: '150px'
  },
  {
    prop: 'specialType',
    label: '是否永久特放',
    minWidth: '150px',
    renderTemplate: (scope) => {
      const specialType = scope.row.specialType
      if (specialType === null) return <span>-</span>
      return <ElTag type={specialType ? 'success' : 'info'}>{specialType ? '是' : '否'}</ElTag>
    }
  },
  {
    prop: 'specialEffective',
    label: '是否有效',
    minWidth: '150px',
    renderTemplate: (scope) => {
      const specialEffective = scope.row.specialEffective
      if (specialEffective === null) return <span>-</span>
      return (
        <ElTag type={specialEffective ? 'success' : 'danger'}>
          {specialEffective ? '是' : '否'}
        </ElTag>
      )
    }
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    minWidth: '220px',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        <ElButton type="primary" link onClick={() => EditBtn(scope.row)}>
          {t('common.edit')}
        </ElButton>
        <ElButton type="primary" link onClick={() => handleDelete(scope.row)}>
          {t('common.delete')}
        </ElButton>
      </div>
    )
  }
])

/**
 * 列表数据
 */
const loading = ref(false)
const tableList = ref([])
const getTableList = async () => {
  loading.value = true
  try {
    const params = {
      customerId: queryForm.value.customerId,
      reviewNo: queryForm.value.reviewNo,
      featureLabelCodeList: queryForm.value.featureLabelCodeList,
      pageNum: queryPage.pageNum || 1,
      pageSize: queryPage.pageSize || 10
    }
    const resData = await getMarkListApi(params)
    tableList.value = resData?.list || []
    queryPage.total = resData?.total || 0
  } catch (error) {
    message.error('获取标记列表失败' + error)
  } finally {
    loading.value = false
  }
}

/**
 * 分页相关
 */
const queryPage = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 改变页数
const handlePageChange = (val: any) => {
  queryPage.pageNum = val
  getTableList()
}
// 改变条数
const handleSizeChange = (val: any) => {
  queryPage.pageNum = 1
  queryPage.pageSize = val
  getTableList()
}

// 搜索
const handleSearch = () => {
  queryPage.pageNum = 1
  queryPage.total = 0
  getTableList()
}
// 重置
const handleReset = () => {
  queryForm.value.customerId = ''
  queryForm.value.reviewNo = ''
  queryForm.value.featureLabelCodeList = []
  // 同时重置客户和图审服务选择
  customerId.value = ''
  reviewNo.value = ''
  queryPage.pageNum = 1
  queryPage.total = 0
  getTableList()
}

// 删除
const handleDelete = (row) => {
  ElMessageBox({
    title: '提示',
    message: `确定要删除该条标记数据吗？`,
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    showCancelButton: true,
    type: 'warning',
    beforeClose: async (action, instance, done) => {
      // 点击取消按钮
      if (action != 'confirm') {
        done()
        return
      }
      // 确定删除
      instance.confirmButtonLoading = true
      try {
        const res = await deleteMarkItemApiNew(row.id)
        if (res?.code == 0) {
          message.success('删除成功')
          getTableList()
        }
      } catch (error) {
        message.error('删除失败' + error)
      } finally {
        instance.confirmButtonLoading = false
        done()
      }
    }
  })
}

// 弹窗相关
const dlgVisable = ref(false)
const dlgType = ref('NewAdd')
const dlgInfo: any = ref(null)
const showDlg = (type, row?) => {
  if (!type) return
  dlgInfo.value = null // 初始化
  if (type == 'Edit') {
    dlgInfo.value = { ...row } || ''
  }
  dlgType.value = type
  dlgVisable.value = true
}

// 关闭弹窗
const closeDialog = (reflashFlag?) => {
  dlgVisable.value = false
  dlgType.value = ''
  if (reflashFlag) {
    queryPage.pageNum = 1
    getTableList()
  }
}

// 反显-格式化类型
const markTypeList = [
  {
    label: '一次性',
    value: 0
  },
  {
    label: '长期',
    value: 1
  }
]

// 反显-格式化目的
const markDescList = [
  {
    label: '通过',
    value: 0
  },
  {
    label: '拒绝',
    value: 1
  }
]

const dialogFrom = ref()
import {
  addMarkListApi,
  editMarkItemApi,
  specialAddApi,
  specialUpdateApi
} from '@/api/RuleManage/MarkGallery'
import { option } from '@/components/dialogFrom/index.vue'
import { getStrDictOptions } from '@/utils/dict'
import envController from '@/controller/envController'
function EditBtn(row) {
  if (row.featureLabelCodeList.includes('SPECIAL')) {
    addEditSpecial(row)
  } else {
    addEditBtn(row)
  }
}
async function addEditBtn(row?: any) {
  const isEdit = !!row
  const formData = isEdit
    ? {
        featureLabelCodeList: row.featureLabelCodeList,
        markImage: [{ name: '', url: row.markImage }],
        reviewNo: row.reviewNo
      }
    : { reviewNo: '', featureLabelCodeList: [], markImage: [] }

  const optionObj: option = {
    title: `${isEdit ? '编辑' : '新增'}标记`,
    formData,
    formList: [
      {
        type: 'select',
        key: 'featureLabelCodeList',
        label: '图片标记标签',
        optionList: getStrDictOptions('feature_label'),
        itemProps: { multiple: true }
      },
      {
        type: 'uploadImage',
        key: 'markImage',
        label: '图片',
        itemProps: { limit: 1, exclusiveId: 'bz-review', targetPath: 'img-mark' }
      },
      {
        type: 'select',
        key: 'reviewNo',
        label: '来源图审服务',
        optionList: reviewConfiglist.value.map((item) => ({
          ...item,
          label: item.reviewName,
          value: item.reviewNo
        })),
        required: false
      }
    ],
    confirm: async (data) => {
      const apiData = {
        reviewNo: data.reviewNo || '',
        markImage:
          `${data.markImage?.[0].url.includes('http') ? '' : envController.getManageUrl()}` +
            data.markImage?.[0].url || '',
        featureLabelCodeList: data.featureLabelCodeList
      }
      let result = '' as any
      if (!isEdit) {
        result = await addMarkListApi(apiData)
      } else {
        result = await editMarkItemApi({ ...apiData, id: row.id })
      }

      if (result) {
        ElMessage.success('标记成功')
        getTableList()
      }
    }
  }
  dialogFrom.value.open(optionObj)
}

async function addEditSpecial(row?: any) {
  const isEdit = !!row
  const formData = isEdit
    ? {
        specialRemark: row.specialRemark,
        markImage: [{ name: '', url: row.markImage }],
        specialAttachedFile: row.specialAttachedFileUrl
          ? [{ name: row.specialAttachedFileName, url: row.specialAttachedFileUrl }]
          : [],
        reviewNo: row.reviewNo,
        specialType: row.specialType
      }
    : {
        reviewNo: '',
        markImage: [],
        specialRemark: '',
        specialAttachedFile: [],
        specialType: false
      }
  const optionObj: option = {
    title: `${isEdit ? '编辑' : '新增'}特批`,
    formData,
    formList: [
      {
        type: 'uploadImage',
        key: 'markImage',
        label: '图片',
        itemProps: { limit: 1, exclusiveId: 'bz-review', targetPath: 'img-mark' }
      },
      {
        type: 'select',
        key: 'reviewNo',
        label: '来源图审服务',
        optionList: reviewConfiglist.value.map((item) => ({
          ...item,
          label: item.reviewName,
          value: item.reviewNo
        }))
      },
      {
        type: 'input',
        key: 'specialRemark',
        itemProps: { type: 'textarea', rows: 4 },
        label: '特批说明'
      },
      {
        type: 'switch',
        key: 'specialType',
        label: '是否永久特放',
        itemProps: {
          activeValue: 1,
          inactiveValue: 0
        }
      },
      {
        type: 'UploadBtn',
        key: 'specialAttachedFile',
        label: '特批附件',
        required: false,
        itemProps: {
          limit: 1
        }
      }
    ],
    confirm: async (data) => {
      console.log('data', data)
      const { specialAttachedFile, ...params } = data
      const objParams = {
        specialAttachedFileUrl: specialAttachedFile?.[0]?.url,
        specialAttachedFileName: specialAttachedFile?.[0]?.name,
        ...params,
        markImage:
          `${data.markImage?.[0].url.includes('http') ? '' : envController.getManageUrl()}` +
            data.markImage?.[0].url || ''
      }
      let result = '' as any
      if (!isEdit) {
        result = await specialAddApi(objParams)
        console.log('isEdit', isEdit, result)
      } else {
        result = await specialUpdateApi({ ...objParams, id: row.id })
      }

      if (result) {
        ElMessage.success('标记特批成功')
        getTableList()
      }
    }
  }
  dialogFrom.value.open(optionObj)
}
</script>

<style scoped lang="less">
.mark-gallery-wrap {
  :deep(.el-select) {
    width: 100%;
  }
}
</style>
