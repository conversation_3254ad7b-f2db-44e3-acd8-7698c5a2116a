<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <!-- 一审列表 -->
  <UmvContent>
    <!-- 查询 -->
    <UmvQuery
      v-model="queryForm"
      :opts="queryOpts"
      label-width="80px"
      @check="handleSearch"
      @reset="handleReset"
    >
      <template #footerBtn>
        <ThePictureSearchBtn
          ref="PictureSearchBtn"
          :page-num="query.page"
          :page-size="query.pageSize"
          type="0"
          :reviewNo="queryForm.reviewNo"
          :submit="PictureSearchSubmit"
        />
      </template>
    </UmvQuery>

    <!-- 列表 -->
    <UmvTable v-loading="loading" :data="tableData" :columns="columns" @refresh="queryPageList">
      <!-- 自定义工具栏 -->
      <template #tools>
        <el-button
          type="primary"
          size="small"
          :loading="exportFileLoading"
          v-track:click.btn
          @click="exportFile"
        >
          导出
        </el-button>
      </template>

      <!-- 分页器 -->
      <template #pagination>
        <Pagination
          :current-page="query.page"
          :page-size="query.pageSize"
          :total="query.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </template>
    </UmvTable>

    <!-- 审核弹窗 -->
    <auditDlg
      v-if="orderAudit"
      :orderAudit="orderAudit"
      :total="query.total"
      :counter="counter"
      :orderId="orderInfo.orderId"
      @closeDialog="closeDialog"
      @next-step="onNextStep"
      @prev-step="onPrevStep"
    />
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'AuditJob'
})

const { t } = useI18n()
import auditDlg from './components/auditDlg.vue'
import ThePictureSearchBtn from '@/views/RuleManage/components/ThePictureSearchBtn'
import { getAuditTaskApi, exportAuditTaskApi } from '@/api/RuleManage/PictureAudit'

import UmvContent from '@/components/UmvContent'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { ElInput, ElSelect, ElOption, ElDatePicker, ElImage, ElButton, ElTag } from 'element-plus'
import { CustomerSelect } from '@/components/CustomerSelect/index'
import { useCustomerReviewSearch } from '@/views/RuleManage/common/useCustomerReviewSearch'

import useMessage from '@/utils/useMessage'
const message = useMessage()

// 使用客户和图审服务选择功能
const {
  customerId,
  reviewNo,
  reviewConfiglist,
  customerList,
  loading: customerLoading,
  changeCusNo,
  changeReviewNo
} = useCustomerReviewSearch()

const loading = ref(false)

const orderAudit = ref(false) // 新增审核项弹窗
const itemTotal = ref(0) //初始化剩余待审

const edit = ref(false) // 是否为编辑
const tableData = ref([{}]) // 用户列表
const stateList = ref([
  // { value: '0', label: '审核中' },
  { value: '1', label: '失败' },
  { value: '2', label: '已完成' }
]) //状态
const resultList = ref([
  { value: '1', label: '通过' },
  { value: '2', label: '拒绝' }
]) //结果
const query = reactive({
  total: 0,
  page: 1,
  pageSize: 10
})

// 查询表单数据
const queryForm = ref({
  customerNo: '', //客户
  reviewName: '', //服务名称
  reviewNo: '', //服务编号
  auditNo: '', //任务编号
  orderNo: '', //调用方ID
  fileName: '', //文件名称
  orderStatus: '', //状态
  orderResult: '', //结果
  time: [], // 订单创建时间
  orderUcode: '',
  auditTime: [] //审核时间
})

// 监听客户和图审服务的变化，同步到查询表单
watch(
  [customerId, reviewNo],
  ([newCustomerId, newReviewNo]) => {
    queryForm.value.customerNo = newCustomerId
    queryForm.value.reviewNo = newReviewNo
  },
  { immediate: true }
)

const orderInfo = ref({
  orderId: ''
}) // 订单信息
const counter = ref(0)

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  customerNo: {
    label: '客户',
    defaultVal: '',
    controlRender: (form: any) => (
      <CustomerSelect
        v-model={customerId.value}
        clearable
        filterable
        placeholder="请选择客户"
        style="width: 100%"
        onChange={changeCusNo}
      />
    )
  },
  reviewNo: {
    label: '图审服务',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect
        v-model={reviewNo.value}
        clearable
        filterable
        placeholder="请选择图审服务"
        style="width: 100%"
        onChange={changeReviewNo}
      >
        {reviewConfiglist.value.map((item) => (
          <ElOption key={item.reviewNo} label={item.reviewName} value={item.reviewNo} />
        ))}
      </ElSelect>
    )
  },
  orderUcode: {
    label: 'U码',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.orderUcode}
        clearable
        maxlength={40}
        placeholder="请输入U码"
        style="width: 100%"
      />
    )
  },
  orderNo: {
    label: '调用方ID',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.orderNo}
        clearable
        maxlength={40}
        placeholder="请输入调用方ID"
        style="width: 100%"
      />
    )
  },
  time: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.time}
        type="daterange"
        value-format="YYYY-MM-DD"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 100%"
      />
    )
  },
  orderStatus: {
    label: '状态',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.orderStatus} clearable placeholder="请选择状态" style="width: 100%">
        {stateList.value.map((item) => (
          <ElOption key={item.value} label={item.label} value={item.value} />
        ))}
      </ElSelect>
    )
  },
  orderResult: {
    label: '结果',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.orderResult} clearable placeholder="请选择结果" style="width: 100%">
        {resultList.value.map((item) => (
          <ElOption key={item.value} label={item.label} value={item.value} />
        ))}
      </ElSelect>
    )
  },
  auditTime: {
    label: '审核时间',
    defaultVal: [],
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.auditTime}
        type="daterange"
        value-format="YYYY-MM-DD"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 100%"
      />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '60px',
    renderTemplate: (scope) => <span>{scope.$index + 1 + (query.page - 1) * query.pageSize}</span>
  },
  {
    prop: 'imageThumbUrl',
    label: '审核图片',
    width: '100px',
    renderTemplate: (scope) => (
      <ElImage
        v-show={scope.row.auditImage}
        fit="fill"
        src={scope.row.auditImage}
        onClick={() => openDialog('orderAudit', scope)}
        style="cursor: pointer"
      />
    )
  },
  {
    prop: 'orderUcode',
    label: 'U码（别名)',
    minWidth: '150px'
  },
  {
    prop: 'reviewName',
    label: '图审服务',
    minWidth: '120px',
    renderTemplate: (scope) => (
      <span class={{ 'color-danger': scope.row.orderTimeOut > 0 }}>{scope.row.reviewName}</span>
    )
  },
  {
    prop: 'customerName',
    label: '客户',
    minWidth: '150px'
  },
  {
    prop: 'orderStatus',
    label: '状态',
    minWidth: '150px',
    renderTemplate: (scope) => {
      const status = scope.row.orderStatus
      let type = 'info'
      let text = ''

      switch (status) {
        case '0':
          type = 'warning'
          text = '审核中'
          break
        case '1':
          type = 'danger'
          text = '失败'
          break
        case '2':
          type = 'success'
          text = '已完成'
          break
        default:
          text = '未知'
      }

      return <ElTag type={type}>{text}</ElTag>
    }
  },
  {
    prop: 'orderResult',
    label: '结果',
    minWidth: '150px',
    renderTemplate: (scope) => {
      const result = scope.row.orderResult
      let type = 'info'
      let text = ''

      switch (result) {
        case '1':
          type = 'success'
          text = '通过'
          break
        case '2':
          type = 'danger'
          text = '拒绝'
          break
        default:
          text = ''
      }

      return text ? <ElTag type={type}>{text}</ElTag> : <span>-</span>
    }
  },
  {
    prop: 'orderNo',
    label: '调用方ID',
    minWidth: '150px'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: '140px'
  },
  {
    prop: 'auditTime',
    label: '审核时间',
    minWidth: '140px'
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '100px',
    renderTemplate: (scope) => (
      <ElButton text type="primary" onClick={() => openDialog('orderAudit', scope)}>
        详情
      </ElButton>
    )
  }
])

onActivated(() => {
  queryPageList()
})

// onMounted(() => {
//   queryPageList()
// })

const exportFileLoading = ref(false)
const exportFile = async () => {
  exportFileLoading.value = true
  const params = {
    customerNo: queryForm.value.customerNo,
    reviewName: queryForm.value.reviewName,
    reviewNo: queryForm.value.reviewNo,
    auditNo: queryForm.value.auditNo, //任务编号
    orderNo: queryForm.value.orderNo, //调用方ID
    fileName: queryForm.value.fileName, //文件名称
    orderStatus: queryForm.value.orderStatus, //状态
    orderResult: queryForm.value.orderResult, //结果
    createTimeStart: queryForm.value?.time?.[0] ?? '',
    createTimeEnd: queryForm.value?.time?.[1] ?? '',
    orderUcode: queryForm.value.orderUcode,
    auditTimeStart: queryForm.value?.auditTime?.[0] || '', //审核时间-开始
    auditTimeEnd: queryForm.value?.auditTime?.[1] || '' //审核时间-结束
  }
  try {
    const res = await exportAuditTaskApi(params)
    const blob = res.data // 文件流
    const disposition = res.headers['content-disposition'].split('filename=')[1] // 请求响应头提取文件名
    const fileName = decodeURIComponent(disposition) // 转码
    window.URL = window.URL || window.webkitURL
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.download = fileName
    link.click()
    window.URL.revokeObjectURL(url)
    // const exportUrl = res.request.responseURL // excel的下载链接
    // window.open(exportUrl, '_self')
  } catch (error) {
    message.error('导出失败！' + error)
  } finally {
    exportFileLoading.value = false
  }
}

// 获取信息列表
const queryPageList = async () => {
  if (loading.value) return // 防止多次发起请求（例如先切换页码，进行条件查询会触发两次请求）
  try {
    loading.value = true
    if (isPictureSearch.value) {
      await PictureSearch()
    } else {
      const data = {
        customerNo: queryForm.value.customerNo, //客户
        reviewName: queryForm.value.reviewName,
        reviewNo: queryForm.value.reviewNo, //服务编号
        auditNo: queryForm.value.auditNo, //任务编号
        orderNo: queryForm.value.orderNo, //调用方ID
        fileName: queryForm.value.fileName, //文件名称
        orderStatus: queryForm.value.orderStatus, //状态
        orderResult: queryForm.value.orderResult, //结果
        createTimeStart: queryForm.value?.time?.[0] ?? '', //创建时间-开始
        orderUcode: queryForm.value.orderUcode,
        createTimeEnd: queryForm.value?.time?.[1] ?? '', //创建时间-结束
        auditTimeStart: queryForm.value?.auditTime?.[0] || '', //审核时间-开始
        auditTimeEnd: queryForm.value?.auditTime?.[1] || '', //审核时间-结束
        pageNum: query.page,
        pageSize: query.pageSize
      }
      const resData = await getAuditTaskApi(data)
      tableData.value = resData?.list || []
      query.total = resData?.total || 0
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}
// 关闭弹窗
const closeDialog = (type = '', flag = false, obj: any = {}) => {
  switch (type) {
    // 审核弹窗
    case 'orderAudit':
      orderAudit.value = false
      if (flag) {
        // query.page = initPageNo.value
        queryPageList()
      } else {
        orderAudit.value = false
        edit.value = false
      }
      break
  }
}
// 打开弹窗
const openDialog = (type = '', obj: any = {}) => {
  switch (type) {
    // 审核弹窗
    case 'orderAudit':
      // this.orderInfoNum = obj.$index
      //orderInfo.value = obj.row
      orderInfo.value.orderId = obj.row.orderId
      counter.value = obj.$index + 1 + (query.page - 1) * query.pageSize
      console.log('orderInfo.value.orderId', obj.row.orderId, 'counter.value', counter.value)
      itemTotal.value = 0
      // initPageNo = this.query.pageNo
      // this.isAudit = false
      // queryTotalNum(obj.row.itemId)
      orderAudit.value = true
      break
  }
}
// 查询
const handleSearch = () => {
  // isSearch.value = !!searchForm.itemId
  query.page = 1
  query.total = 0
  isPictureSearch.value = false
  queryPageList()
}

// 重置
const handleReset = () => {
  queryForm.value.customerNo = ''
  queryForm.value.reviewName = ''
  queryForm.value.reviewNo = ''
  queryForm.value.auditNo = ''
  queryForm.value.orderNo = ''
  queryForm.value.fileName = ''
  queryForm.value.time = []
  queryForm.value.orderUcode = ''
  queryForm.value.orderStatus = ''
  queryForm.value.orderResult = ''
  queryForm.value.auditTime = []
  // 同时重置客户和图审服务选择
  customerId.value = ''
  reviewNo.value = ''
  handleSearch()
}
// 改变页数
const handlePageChange = (val) => {
  query.page = val
  queryPageList()
}
// 改变条数
const handleSizeChange = (val) => {
  query.page = 1
  query.pageSize = val
  queryPageList()
}

// 搜图功能
const PictureSearchBtn = ref()
const isPictureSearch = ref(false)
async function PictureSearchSubmit() {
  isPictureSearch.value = true
  await PictureSearch()
}
async function PictureSearch() {
  const data = await PictureSearchBtn.value.getList()
  tableData.value = data?.list || []
  query.total = data?.total || 0
}

const onNextStep = async (obj) => {
  const tempCounter = obj.counter
  console.log('onNextStep 当前counter', tempCounter)

  if (tempCounter > query.total) {
    //到达最后一页
    counter.value = query.total

    return
  }

  if ((tempCounter - 1) % query.pageSize == 0) {
    //下一页
    //page = Math.floor(tempCounter / query.pageSize) + 1
    query.page = query.page + 1
    console.log('begin queryPageList')
    try {
      await queryPageList()
      const row = tableData.value[0]
      console.log('rowrow', row, 'query.page', query.page)
      orderInfo.value.orderId = row?.orderId
      counter.value++
    } catch (error) {}
  } else {
    //下一个
    //counter.value = obj.$index + 1 + (query.page - 1) * query.pageSize
    const index = tempCounter - (query.page - 1) * query.pageSize - 1
    console.log('下一个 query.page', query.page)
    console.log('index', index)
    const row = tableData.value[index]
    console.log('row', row)
    orderInfo.value.orderId = row?.orderId
    counter.value++
  }
}

const onPrevStep = async (obj) => {
  const tempCounter = obj.counter
  console.log('onPrevStep 当前counter', tempCounter)
  if (tempCounter <= 1) {
    query.page = 1
    const row = tableData.value[0]
    orderInfo.value.orderId = row?.orderId
    counter.value = 1
    return
  }

  if (tempCounter % query.pageSize == 0) {
    //上一页
    //page = Math.floor(tempCounter / query.pageSize) + 1
    try {
      query.page = query.page - 1
      await queryPageList()
      const row = tableData.value[0]
      console.log('rowrow', row, 'query.page', query.page)
      orderInfo.value.orderId = row?.orderId
      counter.value--
    } catch (error) {}
  } else {
    //下一个
    //counter.value = obj.$index + 1 + (query.page - 1) * query.pageSize
    const index = tempCounter - (query.page - 1) * query.pageSize - 1
    console.log('index', index)
    const row = tableData.value[index]
    console.log('row', row)
    orderInfo.value.orderId = row?.orderId
    counter.value--
  }
}

// const onCounterUpdate = (obj) => {
//   const counter = obj.counter
//   let page = 1
//   if (val % query.pageSize == 0) {
//     page = Math.floor(val / query.pageSize)
//   } else {
//     page = Math.floor(val / query.pageSize) + 1
//   }
//   if (page != query.page) {
//     query.page = page
//     queryPageList()
//   }
// }
</script>
